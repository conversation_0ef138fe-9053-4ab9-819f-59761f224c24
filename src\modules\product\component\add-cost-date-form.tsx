import { useState, useEffect, FormEvent } from "react"
import { Plus, Edit, X } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { CostDateEntry } from "@/types/package"

const initialFormState = {
  packageId: 0,
  days: "",
  startDate: "",
  endDate: "",
  price: "",
  discountPrice: "",
  tripStatus: "",
  publish: false,
  upcoming: false,
}

export function AddCostDateForm({
  editingItem,
  onAdd,
  onUpdate,
  onCancelEdit
}: {
  editingItem: CostDateEntry | null,
  onAdd: (data: Omit<CostDateEntry, 'id'>) => void,
  onUpdate: (data: CostDateEntry) => void,
  onCancelEdit: () => void,
}) {
  const [formData, setFormData] = useState(initialFormState)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = editingItem !== null

  useEffect(() => {
    if (isEditing && editingItem) {
      setFormData({
        packageId: editingItem.packageId,
        days: editingItem.days,
        startDate: editingItem.startDate,
        endDate: editingItem.endDate,
        price: editingItem.price,
        discountPrice: editingItem.discountPrice,
        tripStatus: editingItem.tripStatus,
        publish: editingItem.publish,
        upcoming: editingItem.upcoming,
      })
    } else {
      setFormData(initialFormState)
    }
  }, [editingItem, isEditing])

  const handleInput = (field: keyof typeof initialFormState, value: string|boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    try {
      if (isEditing && editingItem) {
        onUpdate({ ...editingItem, ...formData })
      } else {
        onAdd({ ...formData })
      }
      setFormData(initialFormState)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
          {isEditing ? 'Edit Cost & Date' : 'Add Cost & Date'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="days">Days</Label>
            <Input id="days" value={formData.days} onChange={e => handleInput('days', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="startDate">Start Date</Label>
            <Input type="date" id="startDate" value={formData.startDate} onChange={e => handleInput('startDate', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="endDate">End Date</Label>
            <Input type="date" id="endDate" value={formData.endDate} onChange={e => handleInput('endDate', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="price">Price</Label>
            <Input id="price" value={formData.price} onChange={e => handleInput('price', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="discountPrice">Discount Price</Label>
            <Input id="discountPrice" value={formData.discountPrice} onChange={e => handleInput('discountPrice', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="tripStatus">Trip Status</Label>
            <Input id="tripStatus" value={formData.tripStatus} onChange={e => handleInput('tripStatus', e.target.value)} />
          </div>
          <div>
            <label>
              <input
                type="checkbox"
                checked={formData.publish}
                onChange={(e) => handleInput('publish', e.target.checked)}
              /> Publish
            </label>
            <label className="ml-4">
              <input
                type="checkbox"
                checked={formData.upcoming}
                onChange={(e) => handleInput('upcoming', e.target.checked)}
              /> Mark Upcoming Treks
            </label>
          </div>
          <div className="flex justify-end gap-2 pt-4 border-t">
            {isEditing && (
              <Button type="button" variant="outline" onClick={onCancelEdit} disabled={isSubmitting}>
                <X className="w-4 h-4" /> Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : isEditing ? 'Save Changes' : 'Add Cost & Date'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
