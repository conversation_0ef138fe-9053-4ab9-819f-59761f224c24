import React from "react";
import { useGetActivity } from "../queries/use-get-region";
import { Pen, Trash } from "lucide-react";

const RegionsList: React.FC = () => {
    const { data, isLoading, error } = useGetActivity();

    if (isLoading) return <p>Loading regions...</p>;
    if (error) return <p>Error loading regions: {error.message}</p>;

    const regions = data?.data ?? [];

    return (
        <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Regions List</h2>
            {regions.length === 0 ? (
                <p>No regions found.</p>
            ) : (
                <table className="min-w-full border rounded">
                    <thead className="bg-gray-100">
                        <tr>
                            <th className="p-2 border">SN</th>
                            <th className="p-2 border">Name</th>
                            <th className="p-2 border">Slug</th>
                            <th className="p-2 border">Created At</th>
                            <th className="p-2 border">Updated At</th>
                            <th className="p-2 border">Options</th>
                        </tr>
                    </thead>
                    <tbody>
                        {regions.map((region, index) => (
                            <tr key={region.id} className="even:bg-gray-50">
                                <td className="p-2 border">{index + 1}</td>
                                <td className="p-2 border">{region.name}</td>
                                <td className="p-2 border">{region.slug}</td>
                                <td className="p-2 border">{new Date(region.createdAt).toLocaleDateString()}</td>
                                <td className="p-2 border">{new Date(region.updatedAt).toLocaleDateString()}</td>
                                <td className="p-2 border">
                                    <div className="flex space-x-2">
                                        <button
                                            className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                        >
                                            <Pen className="w-4 h-4" />
                                        </button>
                                        <button
                                            className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                            type="button"
                                        >
                                            <Trash className="w-4 h-4" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            )}
        </div>
    );
};

export default RegionsList;
