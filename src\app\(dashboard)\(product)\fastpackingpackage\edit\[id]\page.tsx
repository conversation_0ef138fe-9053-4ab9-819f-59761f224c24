'use client'

import { useState, useEffect, use } from 'react'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { PackageDetailsForm } from '@/modules/product/component/package-detail-form'
import { SeoDetailsSection } from '@/components/package/seo-detail-section'
import { SchemaDetailsSection } from '@/components/package/seo-schema-detail-section'
import { PackageFormData, SeoFields } from '@/types/package'
import { CollapsibleSection } from '@/modules/product/component/collapsible'
import { TripHighlightsContent } from '@/modules/product/component/package-content'
import { HighlightSectionType, PackageContentData } from '@/types/package-form'
import { PackageSidebar } from '@/modules/product/component/package-sidebar'

const getInitialContentData = (): PackageContentData => ({
  highlightsTitle: '8 Highlights of the Package',
  highlightsBody: '• Amazing mountain views during the trek...',
  descriptionTitle: 'Package Description',
  descriptionBody: 'Detailed description of the package...',
  shortItineraryTitle: 'Short Itinerary',
  shortItineraryItems: ['Day 1: Arrival...'],
  photoTitle: 'Photo Gallery',
  photos: [],
  videoTitle: 'Watch Our Trek Video',
  youtubeLinks: [''],
  includesTitle: "WHAT'S INCLUDED",
  includesBody: '• All permits and fees.',
  excludesTitle: "WHAT'S EXCLUDED",
  excludesBody: '• International airfare.',
  mapTitle: 'Route Map',
  tripInfoTitle: 'Package Information',
  tripInfos: []
})

interface EditDetailsPageProps {
  params: Promise<{ id: string }>
}

export default function EditDetailsPage({ params }: EditDetailsPageProps) {
  const { id } = use(params)
  const packageId = parseInt(id)

  const [seoOpen, setSeoOpen] = useState(true)
  const [schemaOpen, setSchemaOpen] = useState(false)
  const [packageEditOpen, setPackageEditOpen] = useState(true)
  const [activeHighlight, setActiveHighlight] = useState<HighlightSectionType>('highlights')
  const [contentData, setContentData] = useState<PackageContentData>(getInitialContentData())

  const [formData, setFormData] = useState<PackageFormData | null>(null)
  const [seoData, setSeoData] = useState<SeoFields>({
    packageId,
    metaTitle: '',
    metaDescription: '',
    metaKeywords: '',
    canonicalUrl: ''
  })
  const [schema, setSchema] = useState<string>('')

  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchData() {
      const demoFormData: PackageFormData = {
        name: 'Everest Base Camp Trek',
        slug: 'everest-base-camp-trek',
        regionId: 'everest',
        activityId: 'fastpackingpackage',
        accomodation: 'Tea House, Lodge',
        distance: '130 km',
        type: 'Mountain Trail',
        duration: '13 Nights 14 Days',
        altitude: '5,364 m | 17,598 ft',
        meals: 'Breakfast, Lunch & Dinner',
        groupSize: '2-12',
        price: '$1,299',
        discountPrice: '$1,199',
        bestSeason: 'March-May / September-November',
        transport: 'Flight / Jeep',
        activityPerDay: '6-8 hrs',
        grade: 'Moderate to Challenging',
        bookingLink: '',
        overviewDescription: '',
        thumbnail: '',
        mainImage: '',
        mainImageAlt: '',
        pdfBrochure: '',
        published: true,
        tripOftheMonth: true,
        popularTour: false,
        shortTrek: false,
      }
      const demoSeoData: SeoFields = {
        packageId,
        metaTitle: "Everest Base Camp Trek - Ultimate Adventure",
        metaDescription: "Join our 14-day Everest Base Camp trek ...",
        metaKeywords: "everest, trekking, nepal, himalaya",
        canonicalUrl: "https://yourdomain.com/everest-base-camp-trek"
      }
      const demoSchema = `{
        "@context": "https://schema.org",
        "@type": "TouristTrip",
        "name": "Everest Base Camp Trek",
        ...
      }`

      setFormData(demoFormData)
      setSeoData(demoSeoData)
      setSchema(demoSchema)
      setLoading(false)
    }
    fetchData()
  }, [packageId])

  const handleContentChange = (field: keyof PackageContentData, value: PackageContentData[keyof PackageContentData]) => {
    setContentData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (loading || !formData) return <p>Loading...</p>

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <EditTabs packageId={packageId} />

      <div className=" space-y-6">
        <CollapsibleSection
          title="Package Details"
          isOpen={packageEditOpen}
          onToggle={() => setPackageEditOpen(!packageEditOpen)}
        >
          <PackageDetailsForm formData={formData} onFormDataChange={setFormData} />
        </CollapsibleSection>

        <CollapsibleSection
          title="SEO Details"
          isOpen={seoOpen}
          onToggle={() => setSeoOpen(!seoOpen)}
        >
          <SeoDetailsSection formData={seoData} setFormData={setSeoData} />
        </CollapsibleSection>
        <CollapsibleSection
          title="Schema Details"
          isOpen={schemaOpen}
          onToggle={() => setSchemaOpen(!schemaOpen)}
        >
          <SchemaDetailsSection schema={schema} setSchema={setSchema} />
        </CollapsibleSection>
      </div>
      <div className="mt-10 grid grid-cols-12 gap-6">
        <div className="col-span-3">
          <PackageSidebar
            activeHighlight={activeHighlight}
            onHighlightChange={setActiveHighlight}
          />
        </div>
        <div className="col-span-9">
          <TripHighlightsContent
            activeHighlight={activeHighlight}
            packageData={contentData}
            onContentChange={handleContentChange}
          />
        </div>
      </div>
    </div>
  )
}
