'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useUpdatePackage } from '@/modules/package/mutations/update-package'
import { useGetActivity } from '@/modules/activity/queries/use-get-activity'
import { useGetRegion } from '@/modules/region/queries/use-get-region'
import { IPackage } from '@/types/package_'
import { Button } from '@/components/ui/button'
import { useGetPackageById } from '@/modules/package/queries/get-package-by-id'
import { PackageDetailsForm } from '@/modules/product/component/package-detail-form'
import { EditTabs } from '@/modules/product/component/edit-tabs'

export default function EditPackagePage() {
  const router = useRouter()
  const { id } = useParams() as { id: string }

  const { data: packageResponse, isLoading, isError } = useGetPackageById(id)
  const { data: activityData, isLoading: activityLoading } = useGetActivity()
  const { data: regionData, isLoading: regionLoading } = useGetRegion()

  const updatePackageMutation = useUpdatePackage(id)

  const [packageData, setPackageData] = useState<Partial<IPackage>>()

  useEffect(() => {
    if (packageResponse?.data) {
      setPackageData(packageResponse.data)
    }
  }, [packageResponse])

  if (isLoading || activityLoading || regionLoading) return <div>Loading...</div>
  if (isError) return <div>Error loading package details.</div>
  if (!packageData) return <div>Loading package data...</div>

  const handleSave = () => {
    updatePackageMutation.mutate(packageData, {
      onSuccess: () => {
        alert('Package updated successfully')
        router.push('/trekpackage') // Or wherever you want to navigate
      },
      onError: (error: Error) => {
        alert(`Error updating package: ${error.message}`)
      },
    })
  }

  return (
    <div className="container mx-auto p-6">
      <EditTabs packageId={id} />
      <PackageDetailsForm
        formData={packageData}
        onFormDataChange={setPackageData}
        activities={activityData?.data || []}
        regions={regionData?.data || []}
      />
      <div className="mt-6 flex justify-end">
        <Button
          onClick={handleSave}
          className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          Update Package
        </Button>
      </div>
    </div>
  )
}




// 'use client'

// import { useRouter } from 'next/navigation'
// import PackagePage from '@/modules/product/template/package-page'
// import { PackageData, PackageFormData, PackageStatusControlsType, PackageContentData, ItineraryItem, SeoFields } from '@/types/package-form'
// import { use } from 'react'

// interface EditTrekkingPackagePageProps {
//   params: Promise<{ id: string }>
// }

// export default function EditTrekkingPackagePage({ params }: EditTrekkingPackagePageProps) {
//   const router = useRouter()

//   const { id } = use(params)
//   const packageId = parseInt(id)
//   const category = "trekpackage"

//   const demoFormData: PackageFormData = {
//     packageName: "Everest Base Camp Trek",
//     region: "everest",
//     accommodation: "Tea House, Lodge",
//     trailType: "Mountain Trail",
//     maxAltitude: "5,364 m | 17,598 ft",
//     groupSize: "2-12",
//     bestSeason: "March-May / September-November",
//     price: "$ 1,299",
//     activityPerDay: "6-8 hrs",
//     grade: "Moderate to Challenging",
//     activity: category,
//     slug: "everest-base-camp-trek",
//     distance: "130 km",
//     daysNights: "13 Nights 14 Days",
//     meals: "Breakfast, Lunch & Dinner",
//     discountPrice: "$ 1,199",
//     transportation: "Flight / Jeep",
//     imageAlt: "Trekkers approaching Everest Base Camp with stunning mountain views",
//     bookingLink: '<button class="wtrvl-checkout_button" id="wetravel_button_widget">Book Now</button>',
//     overview: "<p>The Everest Base Camp Trek is one of the most iconic trekking adventures in the world. This 14-day journey takes you through the heart of the Himalayas to the base camp of the world's highest peak, Mount Everest (8,848m).</p><p>You'll experience breathtaking mountain vistas, rich Sherpa culture, and the satisfaction of reaching 5,364m above sea level. The trek passes through Sagarmatha National Park, home to rare wildlife and stunning rhododendron forests.</p>"
//   }

//   const demoStatusControls: PackageStatusControlsType = {
//     published: true,
//     tripOfTheMonth: true,
//     popularTours: false,
//     shortTrek: false
//   }

//   const demoContentData: PackageContentData = {
//     highlightsTitle: "8 Highlights of the Everest Base Camp Trek",
//     highlightsBody: "<ul><li>Spectacular views of Mount Everest, Lhotse, and Nuptse</li><li>Experience authentic Sherpa culture in traditional villages</li><li>Visit the famous Tengboche Monastery</li><li>Trek through Sagarmatha National Park</li><li>Cross thrilling suspension bridges over deep valleys</li><li>Witness stunning sunrise from Kala Patthar (5,545m)</li><li>Explore the bustling Namche Bazaar</li><li>Achieve the personal accomplishment of reaching EBC</li></ul>",

//     descriptionTitle: "Is the Everest Base Camp trek worth it?",
//     descriptionBody: "<p>Absolutely! The Everest Base Camp trek is a life-changing adventure that combines natural beauty, cultural immersion, and personal achievement. While challenging, the trek is accessible to anyone with good fitness and determination.</p><p>You'll be rewarded with some of the most spectacular mountain scenery on Earth, meet the legendary Sherpa people, and stand at the foot of the world's highest mountain. The sense of accomplishment is unmatched.</p>",

//     shortItineraryTitle: "14-Day Trek Itinerary Overview",
//     shortItineraryItems: [
//       "Day 1: Fly to Lukla, Trek to Phakding (2,610m)",
//       "Day 2-3: Trek to Namche Bazaar (3,440m) - Acclimatization",
//       "Day 4-5: Trek to Tengboche (3,860m) via Monastery",
//       "Day 6-7: Trek to Dingboche (4,410m) - Rest Day",
//       "Day 8-9: Trek to Lobuche (4,910m) via Thukla",
//       "Day 10: Trek to EBC (5,364m) via Gorak Shep",
//       "Day 11: Hike Kala Patthar (5,545m), Descend to Pheriche",
//       "Day 12-14: Return trek to Lukla via Namche Bazaar"
//     ],

//     photoTitle: "Trek Photo Gallery",
//     photos: [
//       { file: null, caption: "Stunning view of Mount Everest from Kala Patthar" },
//       { file: null, caption: "Tengboche Monastery with Ama Dablam in background" },
//       { file: null, caption: "Trekkers crossing suspension bridge in Khumbu valley" }
//     ],

//     videoTitle: "Everest Base Camp Trek Experience",
//     youtubeLinks: [
//       "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
//       "https://www.youtube.com/watch?v=sample-video-2"
//     ],

//     includesTitle: "What's Included in the Trek Package",
//     includesBody: "<ul><li>All domestic flights (Kathmandu-Lukla-Kathmandu)</li><li>All meals during the trek (Breakfast, Lunch, Dinner)</li><li>Tea house accommodation during the trek</li><li>Professional English-speaking guide</li><li>Porter service (1 porter for 2 trekkers)</li><li>All permits and entrance fees</li><li>Comprehensive medical kit</li><li>All government and local taxes</li></ul>",

//     excludesTitle: "What's Not Included",
//     excludesBody: "<ul><li>International airfare to/from Nepal</li><li>Nepal visa fees</li><li>Travel and rescue insurance</li><li>Personal expenses (WiFi, battery charging, hot shower)</li><li>Tips for guide and porter</li><li>Extra nights accommodation in Kathmandu</li><li>Meals in Kathmandu</li><li>Personal trekking equipment</li></ul>",

//     mapTitle: "Everest Base Camp Trek Route Map",

//     tripInfoTitle: "Essential Trek Information",
//     tripInfos: [
//       {
//         title: "Best Time to Trek",
//         body: "<p>The best seasons are Spring (March-May) and Autumn (September-November). These months offer clear mountain views, stable weather, and comfortable temperatures.</p>",
//         note: "Avoid monsoon season (June-August) due to heavy rainfall and poor visibility."
//       },
//       {
//         title: "Fitness Requirements",
//         body: "<p>Good cardiovascular fitness is essential. We recommend 6-8 weeks of preparation including cardio, strength training, and hiking with a loaded backpack.</p>",
//         note: "No technical climbing skills required, but stamina and endurance are crucial."
//       },
//       {
//         title: "Altitude Considerations",
//         body: "<p>Proper acclimatization is vital. Our itinerary includes rest days and gradual altitude gain. Stay hydrated and inform your guide of any symptoms.</p>",
//         note: "We recommend consulting your doctor before the trek, especially if you have pre-existing conditions."
//       }
//     ]
//   }

//   const demoItineraryItems: ItineraryItem[] = [
//     {
//       id: 1,
//       day: "1",
//       title: "Arrival in Kathmandu, Fly to Lukla, Trek to Phakding",
//       details: "<p>Fly from Kathmandu to Lukla (2,840m), one of the world's most scenic flights. Meet your trekking team and begin the trek to Phakding. The trail descends through pine forests with views of Kusum Kanguru and other peaks.</p>",
//       heading: "Journey Begins",
//       trekDistance: "8 km",
//       flightHours: "0.5",
//       drivingHour: "0",
//       highestAltitude: "2,840m",
//       trekDuration: "3-4 hours"
//     },
//     {
//       id: 2,
//       day: "2",
//       title: "Trek from Phakding to Namche Bazaar",
//       details: "<p>Cross multiple suspension bridges over the Dudh Koshi River. Enter Sagarmatha National Park and ascend steeply to Namche Bazaar, the gateway to Everest and unofficial capital of the Khumbu region.</p>",
//       heading: "Gateway to Everest",
//       trekDistance: "11 km",
//       flightHours: "0",
//       drivingHour: "0",
//       highestAltitude: "3,440m",
//       trekDuration: "6-7 hours"
//     },
//     {
//       id: 3,
//       day: "3",
//       title: "Acclimatization Day in Namche Bazaar",
//       details: "<p>Important rest day for acclimatization. Explore Namche Bazaar, visit the local market, and take a short hike to Everest View Hotel for spectacular mountain views including your first glimpse of Mount Everest.</p>",
//       heading: "Acclimatization",
//       trekDistance: "5 km",
//       flightHours: "0",
//       drivingHour: "0",
//       highestAltitude: "3,880m",
//       trekDuration: "3-4 hours"
//     }
//   ]

//   const demoSeo: SeoFields = {
//     metaTitle: "Everest Base Camp Trek - 14 Days | Ultimate Himalayan Adventure",
//     metaDescription: "Join our 14-day Everest Base Camp trek for the ultimate Himalayan adventure. Expert guides, tea house accommodation, and unforgettable mountain views await.",
//     metaKeywords: "everest base camp trek, himalaya trekking, nepal adventure, mount everest, sherpa culture, kala patthar",
//     canonicalUrl: "https://yourwebsite.com/everest-base-camp-trek"
//   }

//   const demoSchema = `{
//   "@context": "https://schema.org",
//   "@type": "TouristTrip", 
//   "name": "Everest Base Camp Trek",
//   "description": "14-day trek to Everest Base Camp through the heart of the Himalayas",
//   "provider": {
//     "@type": "TravelAgency",
//     "name": "Your Trekking Company"
//   },
//   "touristType": "Adventure Travelers",
//   "duration": "P14D",
//   "offers": {
//     "@type": "Offer",
//     "price": "1299",
//     "priceCurrency": "USD"
//   }
// }`

//   const demoPackageData: PackageData = {
//     id: packageId,
//     formData: demoFormData,
//     statusControls: demoStatusControls,
//     contentData: demoContentData,
//     itineraryItems: demoItineraryItems,
//     seo: demoSeo,
//     schema: demoSchema
//   }

//   const handleSave = (data: PackageData) => {
//     console.log('Saving demo package data:', data)
//     alert('Demo data saved! Check console for details.')
//     // In real app: API call here
//     // router.push(`/${category}package`)
//   }

//   const handleCancel = () => {
//     router.push(`/${category}package`)
//   }

//   return (
//     <PackagePage
//       mode="edit"
//       category={category}
//       packageId={packageId}
//       initialData={demoPackageData}
//       onSave={handleSave}
//       onCancel={handleCancel}
//     />
//   )
// }


// // 'use client'

// // import { useRouter } from 'next/navigation'
// // import PackagePage from '@/modules/product/template/package-page'
// // import { PackageData } from '@/types/package-form'

// // interface EditTrekkingPackagePageProps {
// //   params: { id: string }
// // }

// // export default function EditTrekkingPackagePage({ params }: EditTrekkingPackagePageProps) {
// //   const router = useRouter()
// //   const packageId = parseInt(params.id)

// //   const handleSave = (data: PackageData) => {
// //     console.log('Updating trekking package:', data)
// //     router.push('/trekpackage')
// //   }

// //   const handleCancel = () => {
// //     router.push('/trekpackage')
// //   }

// //   return (
// //     <PackagePage
// //       mode="edit"
// //       category="trekpackage"
// //       packageId={packageId}
// //       onSave={handleSave}
// //       onCancel={handleCancel}
// //     />
// //   )
// // }