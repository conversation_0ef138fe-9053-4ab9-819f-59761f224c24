'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { useQueryClient } from '@tanstack/react-query'
import { AddItineraryForm } from '@/modules/product/component/add-itinerary-form'
import { ItineraryList } from '@/modules/product/component/itinerary-list'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { useGetPackageItineraryById } from '@/modules/package-itinerary/queries/get-package-itinerary-by-id'
import { useCreatePackageItinerary } from '@/modules/package-itinerary/mutations/create-package-itinerary'
import { useUpdatePackageItinerary } from '@/modules/package-itinerary/mutations/update-package-itinerary'
import { useDeletePackageItinerary } from '@/modules/package-itinerary/mutations/delete-package-itinerary'
import { IPackageItinerary } from '@/types/package_'
import { toast } from 'sonner'
import { useGetPackageItineraries } from '@/modules/package-itinerary/queries/get-package-itineraries'

export default function EditItineraryPage() {
  const { id: packageId } = useParams() as { id: string }
  const queryClient = useQueryClient()

  const { data, isLoading, isError } = useGetPackageItineraries({ packageId })

  const createMutation = useCreatePackageItinerary()
  const updateMutation = useUpdatePackageItinerary(packageId)
  const deleteMutation = useDeletePackageItinerary(packageId)

  const [items, setItems] = useState<IPackageItinerary[]>([])
  const [editingItem, setEditingItem] = useState<IPackageItinerary | null>(null)

  useEffect(() => {
    if (data?.data) {
      const itineraries: IPackageItinerary[] = Array.isArray(data.data)
        ? data.data.filter(Boolean)
        : [data.data]
      setItems(itineraries)
    }
  }, [data])

  const onAddItinerary = (newItem: Omit<IPackageItinerary, 'id' | 'createdAt' | 'updatedAt'> & { imageFile?: File }) => {
    createMutation.mutate(
      { ...newItem, packageId },
      {
        onSuccess: (res) => {
          if (res.data) {
            setItems((prev) => [...prev, res.data])
            toast.success('Itinerary added successfully')
          }
        },
        onError: () => toast.error('Failed to add itinerary'),
      }
    )
  }

  const onUpdateItinerary = (updatedItem: IPackageItinerary & { imageFile?: File }) => {
    updateMutation.mutate(
      { ...updatedItem, packageId }, 
      {
        onSuccess: () => {
          setEditingItem(null)
          toast.success('Itinerary updated successfully')
        },
        onError: () => toast.error('Failed to update itinerary'),
      }
    )
  }



  const onDeleteItinerary = (id: string) => {
    deleteMutation.mutate(id, {
      onSuccess: () => toast.success('Itinerary deleted successfully'),
      onError: () => toast.error('Failed to delete itinerary'),
    })
  }



  if (isLoading) return <div>Loading itinerary...</div>
  if (isError) return <div>Error loading itinerary data.</div>

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddItineraryForm
          editingItem={editingItem}
          onAddItinerary={onAddItinerary}
          onUpdateItinerary={onUpdateItinerary}
          onCancelEdit={() => setEditingItem(null)}
        />
        <ItineraryList
          items={items}
          onEdit={(id) => setEditingItem(items.find(i => i.id === id) || null)}
          onDelete={onDeleteItinerary}
        />
      </div>
    </div>
  )
}
