'use client'

import { useState, useEffect, use } from 'react'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { AddCostDateForm } from '@/modules/product/component/add-cost-date-form'
import { CostDateList } from '@/modules/product/component/list-cost-date'
import { CostDateEntry } from '@/types/package'
import { Button } from '@/components/ui/button'

interface EditCostDatePageProps {
    params: Promise<{ id: string }>
}

export default function EditCostDatePage({ params }: EditCostDatePageProps) {
    const { id } = use(params)
    const packageId = parseInt(id)

    const [costDateItems, setCostDateItems] = useState<CostDateEntry[]>([])
    const [editingCostDate, setEditingCostDate] = useState<CostDateEntry | null>(null)

    useEffect(() => {
        const demoCostDateItems: CostDateEntry[] = [
            {
                id: 1,
                packageId: Number(packageId),
                days: '10',
                startDate: '2025-09-01',
                endDate: '2025-09-10',
                price: '$1200',
                discountPrice: '$1100',
                tripStatus: 'Available',
                publish: true,
                upcoming: true,
            },
            {
                id: 2,
                packageId: Number(packageId),
                days: '7',
                startDate: '2025-11-01',
                endDate: '2025-11-07',
                price: '$900',
                discountPrice: '$850',
                tripStatus: 'Available',
                publish: true,
                upcoming: false,
            },
        ]
        setCostDateItems(demoCostDateItems)
    }, [packageId])

    const handleAddCostDate = (data: Omit<CostDateEntry, 'id'>) => {
        setCostDateItems(prev => [...prev, { ...data, id: Date.now() }])
    }

    const handleEditCostDate = (id: number) => {
        const found = costDateItems.find(item => item.id === id) || null
        setEditingCostDate(found)
    }

    const handleUpdateCostDate = (updatedItem: CostDateEntry) => {
        setCostDateItems(items =>
            items.map(item => (item.id === updatedItem.id ? updatedItem : item))
        )
        setEditingCostDate(null)
    }

    const handleDeleteCostDate = (id: number) => {
        setCostDateItems(items => items.filter(item => item.id !== id))
        if (editingCostDate?.id === id) setEditingCostDate(null)
    }

    const handleCancelEditCostDate = () => setEditingCostDate(null)

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <EditTabs packageId={packageId} />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                <div>
                    <AddCostDateForm
                        editingItem={editingCostDate}
                        onAdd={handleAddCostDate}
                        onUpdate={handleUpdateCostDate}
                        onCancelEdit={handleCancelEditCostDate}
                    />
                </div>
                <div>
                    <CostDateList
                        items={costDateItems}
                        onEdit={handleEditCostDate}
                        onDelete={handleDeleteCostDate}
                    />
                </div>
            </div>
            <div>
                <Button
                    className="mt-9 px-4 py-2 bg-brand text-white rounded-lg"
                >
                    Save
                </Button>
            </div>
        </div>
    )
}
