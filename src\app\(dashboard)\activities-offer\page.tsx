"use client";

import React, { useState } from "react";

import { IActivity } from "@/types/activity";
import { useGetActivity } from "@/modules/activity/queries/use-get-activity";
import EditActivity from "@/modules/activity/components/edit-activity";
import { Pen, Trash } from "lucide-react";

const ActivitiesPage: React.FC = () => {
    const { data, isLoading, error } = useGetActivity();
    const [editingActivity, setEditingActivity] = useState<IActivity | null>(null);

    if (isLoading) return <p>Loading activities...</p>;
    if (error) return <p>Error loading activities: {error.message}</p>;

    const activities = data?.data ?? [];

    return (
        <div className="p-6">
            {!editingActivity ? (
                <>
                    <h1 className="text-2xl font-bold mb-4">Activities List</h1>
                    <table className="min-w-full border rounded">
                        <thead className="bg-gray-100">
                            <tr>
                                <th className="p-2 border">SN</th>
                                <th className="p-2 border">Name</th>
                                <th className="p-2 border">Slug</th>
                                <th className="p-2 border">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {activities.map((activity, idx) => (
                                <tr key={activity.id} className={idx % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                                    <td className="p-2 border">{idx + 1}</td>
                                    <td className="p-2 border">{activity.name}</td>
                                    <td className="p-2 border">{activity.slug}</td>
                                    <td className="p-2 border">
                                        <div className="flex space-x-2">
                                            <button
                                                className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                                onClick={() => setEditingActivity(activity)}
                                            >
                                                <Pen className="w-4 h-4" />
                                            </button>
                                            <button
                                                // onClick={handleDelete}
                                                className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                                type="button"
                                            >
                                                <Trash className="w-4 h-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </>
            ) : (
                <>
                    <button
                        className="mb-4 px-4 py-2 rounded border hover:bg-gray-100"
                        onClick={() => setEditingActivity(null)}
                    >
                        Back to List
                    </button>
                    <EditActivity initialActivity={editingActivity} />
                </>
            )}
        </div>
    );
};

export default ActivitiesPage;
