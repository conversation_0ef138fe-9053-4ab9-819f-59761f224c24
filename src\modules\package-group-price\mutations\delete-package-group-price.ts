import { IPackageGroupPrice } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeletePackageGroupPrice(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageGroupPrice>, Error, any>({
    mutationFn: () =>
      fetch(`/api/package-group-price/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages'] });
      toast.success('Package deleted successfully');
    },
    onError: () => {
      toast.error('Error deleting package');
    },
  });
}
