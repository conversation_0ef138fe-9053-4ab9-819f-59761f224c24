'use client'

import { useState, useEffect, use } from 'react'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { AddEquipmentForm } from '@/modules/product/component/add-equipment-form'
import { EquipmentEntry } from '@/types/package'
import { EquipmentList } from '@/modules/product/component/euqipment-list'
import { Button } from '@/components/ui/button'

interface EditEquipmentPageProps {
    params: Promise<{ id: string }>
}

export default function EditEquipmentPage({ params }: EditEquipmentPageProps) {
    const { id } = use(params)
    const packageId = parseInt(id)

  const [equipmentItems, setEquipmentItems] = useState<EquipmentEntry[]>([])
  const [editingEquipment, setEditingEquipment] = useState<EquipmentEntry | null>(null)

  useEffect(() => {
    const demoEquipment: EquipmentEntry[] = [
      { id: 1, packageId: Number(packageId), title: 'Backpack', description: '50L capacity', head: 'Cap', face: 'Sunglasses', body: 'Layered clothing' },
      { id: 2, packageId: Number(packageId), title: 'Sleeping Bag', description: 'Down sleeping bag', head: '', face: '', body: '' },
    ]
    setEquipmentItems(demoEquipment)
  }, [packageId])

  const handleAddEquipment = (data: Omit<EquipmentEntry, 'id'>) => {
    setEquipmentItems(prev => [...prev, { ...data, id: Date.now() }])
  }

  const handleEditEquipment = (id: number) => {
    const found = equipmentItems.find(item => item.id === id) || null
    setEditingEquipment(found)
  }

  const handleUpdateEquipment = (updated: EquipmentEntry) => {
    setEquipmentItems(items =>
      items.map(item => (item.id === updated.id ? updated : item))
    )
    setEditingEquipment(null)
  }

  const handleDeleteEquipment = (id: number) => {
    setEquipmentItems(items => items.filter(item => item.id !== id))
    if (editingEquipment?.id === id) setEditingEquipment(null)
  }

  const handleCancelEdit = () => setEditingEquipment(null)

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <EditTabs packageId={packageId} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <div>
          <AddEquipmentForm
            editingEquipment={editingEquipment}
            onAddEquipment={handleAddEquipment}
            onUpdateEquipment={handleUpdateEquipment}
            onCancelEdit={handleCancelEdit}
          />
        </div>
        <div>
          <EquipmentList
            items={equipmentItems}
            onEdit={handleEditEquipment}
            onDelete={handleDeleteEquipment}
          />
        </div>
      </div>
      <div>
        <Button
          className="mt-9 px-4 py-2 bg-brand text-white rounded-lg"
        >
          Save
        </Button>
      </div>
    </div>
  )
}
