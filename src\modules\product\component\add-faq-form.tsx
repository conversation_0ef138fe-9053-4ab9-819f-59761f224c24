import { useState, useEffect, FormEvent } from "react"
import { Plus, Edit, X } from 'lucide-react'
import { FAQEntry } from "@/types/package"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

const initialFormState = {
  packageId: 0,
  question: "",
  answer: "",
  publish: false,
}

export function AddFAQForm({
  editingItem,
  onAdd,
  onUpdate,
  onCancelEdit
}: {
  editingItem: FAQEntry | null,
  onAdd: (data: Omit<FAQEntry, 'id'>) => void,
  onUpdate: (data: FAQEntry) => void,
  onCancelEdit: () => void,
}) {
  const [formData, setFormData] = useState(initialFormState)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = editingItem !== null

  useEffect(() => {
    if (isEditing && editingItem) {
      setFormData({
        packageId: editingItem.packageId,
        question: editingItem.question,
        answer: editingItem.answer,
        publish: editingItem.publish || false
      })
    } else {
      setFormData(initialFormState)
    }
  }, [editingItem, isEditing])

  const handleInput = (field: keyof typeof initialFormState, value: string|boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    try {
      if (isEditing && editingItem) {
        onUpdate({ ...editingItem, ...formData })
      } else {
        onAdd({ ...formData })
      }
      setFormData(initialFormState)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
          {isEditing ? 'Edit FAQ' : 'Add FAQ'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="question">Question</Label>
            <Input
              id="question"
              value={formData.question}
              onChange={e => handleInput('question', e.target.value)}
              required
              placeholder='Enter question'
            />
          </div>
          <div>
            <Label htmlFor="answer">Answer</Label>
            <Input
              id="answer"
              value={formData.answer}
              onChange={e => handleInput('answer', e.target.value)}
              required
              placeholder='Enter answer'
            />
          </div>
          <div>
            <label>
              <input
                type="checkbox"
                checked={formData.publish}
                onChange={e => handleInput('publish', e.target.checked)}
              /> Publish
            </label>
          </div>
          <div className="flex justify-end gap-2 pt-4 border-t">
            {isEditing && (
              <Button type="button" variant="outline" onClick={onCancelEdit} disabled={isSubmitting}>
                <X className="w-4 h-4" /> Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : isEditing ? 'Save Changes' : 'Add FAQ'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
