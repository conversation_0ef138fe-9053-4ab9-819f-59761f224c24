'use client'

import { useState, useEffect, use } from 'react'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { FAQEntry } from '@/types/package'
import { AddFAQForm } from '@/modules/product/component/add-faq-form'
import { FAQList } from '@/modules/product/component/list-faq'
import { Button } from '@/components/ui/button'

interface EditFAQPageProps {
  params: Promise<{ id: string }>
}

export default function EditFAQPage({ params }: EditFAQPageProps) {
    const { id } = use(params)
    const packageId = parseInt(id)

  const [faqItems, setFaqItems] = useState<FAQEntry[]>([])
  const [editingFaq, setEditingFaq] = useState<FAQEntry | null>(null)

  useEffect(() => {
    // Example demo data; replace with real API fetch
    const demoFAQs: FAQEntry[] = [
      {
        id: 1,
        packageId: Number(packageId),
        question: 'What is the best time to visit?',
        answer: 'The best time is from March to May and September to November.',
        publish: true,
      },
      {
        id: 2,
        packageId: Number(packageId),
        question: 'Is prior trekking experience required?',
        answer: 'No prior experience is needed but good physical fitness is recommended.',
        publish: false,
      }
    ]
    setFaqItems(demoFAQs)
  }, [packageId])

  const handleAddFaq = (data: Omit<FAQEntry, 'id'>) => {
    setFaqItems(prev => [...prev, { ...data, id: Date.now() }])
  }

  const handleEditFaq = (id: number) => {
    const found = faqItems.find(item => item.id === id) || null
    setEditingFaq(found)
  }

  const handleUpdateFaq = (updatedItem: FAQEntry) => {
    setFaqItems(items =>
      items.map(item => (item.id === updatedItem.id ? updatedItem : item))
    )
    setEditingFaq(null)
  }

  const handleDeleteFaq = (id: number) => {
    setFaqItems(items => items.filter(item => item.id !== id))
    if (editingFaq?.id === id) setEditingFaq(null)
  }

  const handleCancelEditFaq = () => setEditingFaq(null)

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <EditTabs packageId={packageId} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <div>
          <AddFAQForm
            editingItem={editingFaq}
            onAdd={handleAddFaq}
            onUpdate={handleUpdateFaq}
            onCancelEdit={handleCancelEditFaq}
          />
        </div>
        <div>
          <FAQList
            items={faqItems}
            onEdit={handleEditFaq}
            onDelete={handleDeleteFaq}
          />
        </div>
      </div>
      <div>
        <Button
          className="mt-9 px-4 py-2 bg-brand text-white rounded-lg"
        >
          Save
        </Button>
      </div>
    </div>
  )
}
