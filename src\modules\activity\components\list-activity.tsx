"use client";

import React from "react";
import Link from "next/link";
import { Pen, Trash } from "lucide-react";
import { useGetActivity } from "@/modules/activity/queries/use-get-activity";

const ListActivityOffer: React.FC = () => {
  const { data, isLoading, error } = useGetActivity();

  if (isLoading) return <p>Loading activities...</p>;
  if (error) return <p>Error loading activities: {error.message}</p>;

  const activities = data?.data ?? [];

  return (
    <div className="p-6">
      <div className="mb-4 flex justify-between items-center">
        <h2 className="text-xl font-semibold">All Activities offer</h2>
        <button
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition"
          onClick={() => window.location.href = "/home/<USER>/create"}
        >
          Add Activities Offer
        </button>
      </div>

      <div className="bg-white rounded-lg shadow border p-6">
        <table className="min-w-full border-separate border-spacing-y-1">
          <thead>
            <tr className="bg-gray-100">
              <th className="border px-4 py-2">SN</th>
              <th className="border px-4 py-2">Title</th>
              <th className="border px-4 py-2">Slug</th>
              <th className="border px-4 py-2">Description</th>
              <th className="border px-4 py-2">Options</th>
            </tr>
          </thead>
          <tbody>
            {activities.map((activity, idx) => (
              <tr key={activity.id} className="even:bg-gray-50">
                <td className="border px-4 py-2">{idx + 1}</td>
                <td className="border px-4 py-2">{activity.name}</td>
                <td className="border px-4 py-2">{activity.slug}</td>
                <td className="border px-4 py-2">{activity.description}</td>
                <td className="border px-4 py-2 flex gap-2">
                  <Link
                    href={`/home/<USER>/edit/${activity.id}`}
                    className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50"
                  >
                    <Pen className="w-4 h-4" />
                  </Link>
                  <button
                    className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50"
                    type="button"
                  >
                    <Trash className="w-4 h-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ListActivityOffer;
