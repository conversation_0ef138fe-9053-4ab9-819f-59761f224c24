'use client'

import { useState, useEffect, use } from 'react'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { AddGroupDiscountForm } from '@/modules/product/component/add-groupdiscount'
import { GroupDiscountList } from '@/modules/product/component/list-group-discount'
import { GroupDiscountEntry } from '@/types/package'
import { Button } from '@/components/ui/button'

interface EditGroupDiscountPageProps {
    params: Promise<{ id: string }>
}

export default function EditGroupDiscountPage({ params }: EditGroupDiscountPageProps) {
    const { id } = use(params)
    const packageId = parseInt(id)

    const [groupDiscountItems, setGroupDiscountItems] = useState<GroupDiscountEntry[]>([])
    const [editingGroupDiscount, setEditingGroupDiscount] = useState<GroupDiscountEntry | null>(null)

  useEffect(() => {
    const demoDiscounts: GroupDiscountEntry[] = [
      {
        id: 1,
        packageId: Number(packageId),
        groupSize: '4-6',
        pricePerPerson: '$1000',
        note: 'Family discount',
        publish: true
      },
      {
        id: 2,
        packageId: Number(packageId),
        groupSize: '7-10',
        pricePerPerson: '$900',
        note: 'Group discount',
        publish: false
      },
    ]
    setGroupDiscountItems(demoDiscounts)
  }, [packageId])

  const handleAddGroupDiscount = (data: Omit<GroupDiscountEntry, 'id'>) => {
    setGroupDiscountItems(prev => [...prev, { ...data, id: Date.now() }])
  }

  const handleEditGroupDiscount = (id: number) => {
    const found = groupDiscountItems.find(item => item.id === id) || null
    setEditingGroupDiscount(found)
  }

  const handleUpdateGroupDiscount = (updated: GroupDiscountEntry) => {
    setGroupDiscountItems(items =>
      items.map(item => (item.id === updated.id ? updated : item))
    )
    setEditingGroupDiscount(null)
  }

  const handleDeleteGroupDiscount = (id: number) => {
    setGroupDiscountItems(items => items.filter(item => item.id !== id))
    if (editingGroupDiscount?.id === id) setEditingGroupDiscount(null)
  }

  const handleCancelEditGroupDiscount = () => setEditingGroupDiscount(null)

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <EditTabs packageId={packageId} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <div>
          <AddGroupDiscountForm
            editingItem={editingGroupDiscount}
            onAdd={handleAddGroupDiscount}
            onUpdate={handleUpdateGroupDiscount}
            onCancelEdit={handleCancelEditGroupDiscount}
          />
        </div>
        <div>
          <GroupDiscountList
            items={groupDiscountItems}
            onEdit={handleEditGroupDiscount}
            onDelete={handleDeleteGroupDiscount}
          />
        </div>
      </div>
      <div>
        <Button
          className="mt-9 px-4 py-2 bg-brand text-white rounded-lg"
        >
          Save
        </Button>
      </div>
    </div>
  )
}
