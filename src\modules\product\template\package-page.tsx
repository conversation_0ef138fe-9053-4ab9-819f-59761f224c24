'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, MapPin, Users, DollarSign, HelpCircle } from 'lucide-react'

// import { PackageDetailsForm } from '../component/package-detail-form'
import { PackageStatusControls } from '../component/package-status-control'
import { CollapsibleSection } from '../component/collapsible'
import { AddItineraryForm } from '../component/add-itinerary-form'
import { ItineraryList } from '../component/itinerary-list'
import { TripHighlightsContent } from '../component/package-content'
import { PackageSidebar } from '../component/package-sidebar'
import { SeoDetailsSection } from '@/components/package/seo-detail-section'
import { SchemaDetailsSection } from '@/components/package/seo-schema-detail-section'

import {
  PackagePageProps,
  PackageData,
  ItineraryItem,
  SeoFields,
  PackageFormData,
  PackageStatusControlsType,
  PackageContentData,
  PackageTabType,
  HighlightSectionType,
  ActivityCategory,
  EquipmentEntry,
  CostDateEntry,
  GroupDiscountEntry,
  FAQEntry,
  ReviewEntry
} from '@/types/package-form'
import { AddEquipmentForm } from '../component/add-equipment-form'
import { EquipmentList } from '../component/euqipment-list'
import { AddCostDateForm } from '../component/add-cost-date-form'
import { CostDateList } from '../component/list-cost-date'
import { AddGroupDiscountForm } from '../component/add-groupdiscount'
import { GroupDiscountList } from '../component/list-group-discount'
import { AddFAQForm } from '../component/add-faq-form'
import { FAQList } from '../component/list-faq'
import { AddReviewForm } from '../component/add-review-form'
import { ReviewList } from '../component/list-review'

const getInitialFormData = (category?: ActivityCategory): PackageFormData => ({
  packageName: '',
  region: '',
  accommodation: '',
  trailType: '',
  maxAltitude: '',
  groupSize: '',
  bestSeason: '',
  price: '',
  activityPerDay: '',
  grade: '',
  activity: category || 'trekpackage',
  slug: '',
  distance: '',
  daysNights: '',
  meals: '',
  discountPrice: '',
  transportation: '',
  imageAlt: '',
  bookingLink: '',
  overview: ''
})

const getInitialStatusControls = (): PackageStatusControlsType => ({
  published: false,
  tripOfTheMonth: false,
  popularTours: false,
  shortTrek: false
})

const getInitialContentData = (): PackageContentData => ({
  highlightsTitle: '8 Highlights of the Package',
  highlightsBody: '• Amazing mountain views during the trek...',
  descriptionTitle: 'Package Description',
  descriptionBody: 'Detailed description of the package...',
  shortItineraryTitle: 'Short Itinerary',
  shortItineraryItems: ['Day 1: Arrival...'],
  photoTitle: 'Photo Gallery',
  photos: [],
  videoTitle: 'Watch Our Trek Video',
  youtubeLinks: [''],
  includesTitle: "WHAT'S INCLUDED",
  includesBody: '• All permits and fees.',
  excludesTitle: "WHAT'S EXCLUDED",
  excludesBody: '• International airfare.',
  mapTitle: 'Route Map',
  tripInfoTitle: 'Package Information',
  tripInfos: []
})

const getInitialSeoFields = (): SeoFields => ({
  metaTitle: '',
  metaDescription: '',
  metaKeywords: '',
  canonicalUrl: ''
})

const getInitialItineraryItems = (): ItineraryItem[] => [
  {
    id: 1,
    day: "1",
    title: "Sample Day 1",
    details: "Sample details for day 1",
    image: '/images/random.jpeg',
    heading: 'Day 1 Heading',
    trekDistance: '5km',
    flightHours: '0',
    drivingHour: '3',
    highestAltitude: '1940m',
    trekDuration: '4 hours'
  }
]

export default function PackagePage({
  mode = 'create',
  category,
  packageId,
  initialData,
  onSave,
  onCancel
}: PackagePageProps) {
  const [activeTab, setActiveTab] = useState<PackageTabType>('details')
  const [editingItem, setEditingItem] = useState<ItineraryItem | null>(null)
  const [seoOpen, setSeoOpen] = useState(false)
  const [schemaOpen, setSchemaOpen] = useState(false)
  // const [packageEditOpen, setPackageEditOpen] = useState(true)
  const [activeHighlight, setActiveHighlight] = useState<HighlightSectionType>('highlights')

  const [formData, setFormData] = useState<PackageFormData>(getInitialFormData(category))
  const [statusControls, setStatusControls] = useState<PackageStatusControlsType>(getInitialStatusControls())
  const [contentData, setContentData] = useState<PackageContentData>(getInitialContentData())
  const [itineraryItems, setItineraryItems] = useState<ItineraryItem[]>([])
  const [seo, setSeo] = useState<SeoFields>(getInitialSeoFields())
  const [schema, setSchema] = useState<string>('')

  const [equipmentItems, setEquipmentItems] = useState<EquipmentEntry[]>([])
  const [editingEquipment, setEditingEquipment] = useState<EquipmentEntry | null>(null)

  const [costDateItems, setCostDateItems] = useState<CostDateEntry[]>([])
  const [editingCostDate, setEditingCostDate] = useState<CostDateEntry | null>(null)

  const [groupDiscountItems, setGroupDiscountItems] = useState<GroupDiscountEntry[]>([])
  const [editingGroupDiscount, setEditingGroupDiscount] = useState<GroupDiscountEntry | null>(null)

  const [faqItems, setFaqItems] = useState<FAQEntry[]>([])
  const [editingFaq, setEditingFaq] = useState<FAQEntry | null>(null)

  const [reviewItems, setReviewItems] = useState<ReviewEntry[]>([])
  const [editingReview, setEditingReview] = useState<ReviewEntry | null>(null)

  useEffect(() => {
    if (mode === 'edit' && initialData) {
      setFormData(initialData.formData)
      setStatusControls(initialData.statusControls)
      setContentData(initialData.contentData)
      setItineraryItems(initialData.itineraryItems)
      setSeo(initialData.seo)
      setSchema(initialData.schema)
    } else if (mode === 'create') {
      setFormData(getInitialFormData(category))
      setItineraryItems(getInitialItineraryItems())
    }
  }, [mode, initialData, category])

  useEffect(() => {
    if (mode === 'edit' && packageId && !initialData) {
      // TODO: Fetch package data from API
      // fetchPackageData(packageId).then(setInitialData)
    }
  }, [mode, packageId, initialData])

  const handleAddItinerary = (newItemData: Omit<ItineraryItem, 'id'>) => {
    const newItem = {
      ...newItemData,
      id: Date.now(),
      image: newItemData.imageFile ? URL.createObjectURL(newItemData.imageFile) : undefined,
    }
    setItineraryItems((prevItems) => [...prevItems, newItem])
  }

  const handleEditItinerary = (id: number) => {
    const itemToEdit = itineraryItems.find((item) => item.id === id)
    if (itemToEdit) {
      setEditingItem(itemToEdit)
    }
  }

  const handleUpdateItinerary = (updatedItemData: ItineraryItem) => {
    if (updatedItemData.imageFile) {
      updatedItemData.image = URL.createObjectURL(updatedItemData.imageFile)
    }

    setItineraryItems(itineraryItems.map((item) =>
      item.id === updatedItemData.id ? updatedItemData : item
    ))
    setEditingItem(null)
  }

  const handleDeleteItinerary = (id: number) => {
    setItineraryItems(items => items.filter(item => item.id !== id))
    if (editingItem && editingItem.id === id) {
      setEditingItem(null)
    }
  }


  const handleAddEquipment = (data: Omit<EquipmentEntry, 'id'>) => {
    setEquipmentItems(prev => [...prev, { ...data, id: Date.now() }])
  }
  const handleEditEquipment = (id: number) => {
    const found = equipmentItems.find(item => item.id === id)
    setEditingEquipment(found || null)
  }
  const handleUpdateEquipment = (data: EquipmentEntry) => {
    setEquipmentItems(items =>
      items.map(item => (item.id === data.id ? data : item))
    )
    setEditingEquipment(null)
  }
  const handleDeleteEquipment = (id: number) => {
    setEquipmentItems(prev => prev.filter(item => item.id !== id))
    if (editingEquipment && editingEquipment.id === id) {
      setEditingEquipment(null)
    }
  }
  const handleCancelEditEquipment = () => setEditingEquipment(null)

  const handleCancelEdit = () => {
    setEditingItem(null)
  }

  const handleAddCostDate = (data: Omit<CostDateEntry, 'id'>) =>
    setCostDateItems(prev => [...prev, { ...data, id: Date.now() }])

  const handleEditCostDate = (id: number) => {
    const found = costDateItems.find(item => item.id === id)
    setEditingCostDate(found || null)
  }
  const handleUpdateCostDate = (data: CostDateEntry) => {
    setCostDateItems(items =>
      items.map(item => (item.id === data.id ? data : item))
    )
    setEditingCostDate(null)
  }
  const handleDeleteCostDate = (id: number) => {
    setCostDateItems(prev => prev.filter(item => item.id !== id))
    if (editingCostDate && editingCostDate.id === id) setEditingCostDate(null)
  }
  const handleCancelEditCostDate = () => setEditingCostDate(null)

  const handleAddGroupDiscount = (data: Omit<GroupDiscountEntry, 'id'>) =>
    setGroupDiscountItems(prev => [...prev, { ...data, id: Date.now() }])

  const handleEditGroupDiscount = (id: number) => {
    const found = groupDiscountItems.find(item => item.id === id)
    setEditingGroupDiscount(found || null)
  }
  const handleUpdateGroupDiscount = (data: GroupDiscountEntry) => {
    setGroupDiscountItems(items =>
      items.map(item => (item.id === data.id ? data : item))
    )
    setEditingGroupDiscount(null)
  }
  const handleDeleteGroupDiscount = (id: number) => {
    setGroupDiscountItems(prev => prev.filter(item => item.id !== id))
    if (editingGroupDiscount && editingGroupDiscount.id === id) setEditingGroupDiscount(null)
  }
  const handleCancelEditGroupDiscount = () => setEditingGroupDiscount(null)

  const handleAddFaq = (data: Omit<FAQEntry, 'id'>) =>
    setFaqItems(prev => [...prev, { ...data, id: Date.now() }])

  const handleEditFaq = (id: number) => {
    const found = faqItems.find(item => item.id === id)
    setEditingFaq(found || null)
  }
  const handleUpdateFaq = (data: FAQEntry) => {
    setFaqItems(items =>
      items.map(item => (item.id === data.id ? data : item))
    )
    setEditingFaq(null)
  }
  const handleDeleteFaq = (id: number) => {
    setFaqItems(prev => prev.filter(item => item.id !== id))
    if (editingFaq && editingFaq.id === id) setEditingFaq(null)
  }
  const handleCancelEditFaq = () => setEditingFaq(null)

  const handleAddReview = (data: Omit<ReviewEntry, 'id'>) => {
    const newItem = {
      ...data,
      id: Date.now(),
      image: data.imageFile ? URL.createObjectURL(data.imageFile) : undefined
    }
    setReviewItems(prev => [...prev, newItem])
  }

  const handleEditReview = (id: number) => {
    const found = reviewItems.find(item => item.id === id)
    setEditingReview(found || null)
  }

  const handleUpdateReview = (data: ReviewEntry) => {
    if (data.imageFile) {
      data.image = URL.createObjectURL(data.imageFile)
    }
    setReviewItems(prev => prev.map(item => item.id === data.id ? data : item))
    setEditingReview(null)
  }

  const handleDeleteReview = (id: number) => {
    setReviewItems(prev => prev.filter(item => item.id !== id))
    if (editingReview?.id === id) setEditingReview(null)
  }

  const handleCancelEditReview = () => setEditingReview(null)

  const handleContentChange = (field: keyof PackageContentData, value: PackageContentData[keyof PackageContentData]) => {
    setContentData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    const packageData: PackageData = {
      id: packageId,
      formData,
      statusControls,
      contentData,
      itineraryItems,
      seo,
      schema
    }

    if (onSave) {
      onSave(packageData)
    } else {
      // Default save logic (could be API call)
      console.log('Saving package data:', packageData)
      // TODO: Implement save logic
    }
  }

  const pageTitle = mode === 'create' ? 'Create New Package' : 'Edit Package'

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">{pageTitle}</h1>
          <div className="flex gap-2">
            {onCancel && (
              <button
                onClick={onCancel}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
            )}
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              {mode === 'create' ? 'Create Package' : 'Save Changes'}
            </button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as PackageTabType)} className="w-full">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="details" className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Package Details
            </TabsTrigger>
            <TabsTrigger value="itinerary" className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Itinerary
            </TabsTrigger>
            <TabsTrigger value="equipment" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Equipment
            </TabsTrigger>
            <TabsTrigger value="cost" className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Cost and Date
            </TabsTrigger>
            <TabsTrigger value="discount" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Group Discount Prices
            </TabsTrigger>
            <TabsTrigger value="faq" className="flex items-center gap-2">
              <HelpCircle className="w-4 h-4" />
              Package FAQ
            </TabsTrigger>
            <TabsTrigger value="review" className="flex items-center gap-2">
              <HelpCircle className="w-4 h-4" />
              Package Review
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            <PackageStatusControls
              statusControls={statusControls}
              onStatusChange={setStatusControls}
            />

            <div className="space-y-4">
              <CollapsibleSection
                title="SEO Details"
                isOpen={seoOpen}
                onToggle={() => setSeoOpen(!seoOpen)}
              >
                <SeoDetailsSection formData={seo} setFormData={setSeo} />
              </CollapsibleSection>

              <CollapsibleSection
                title="Schema Details"
                isOpen={schemaOpen}
                onToggle={() => setSchemaOpen(!schemaOpen)}
              >
                <SchemaDetailsSection schema={schema} setSchema={setSchema} />
              </CollapsibleSection>

              {/* <CollapsibleSection
                title="Package Edit"
                isOpen={packageEditOpen}
                onToggle={() => setPackageEditOpen(!packageEditOpen)}
              >
                <PackageDetailsForm
                  formData={formData}
                  onFormDataChange={setFormData}
                />
              </CollapsibleSection> */}
            </div>

            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-3">
                <PackageSidebar
                  activeHighlight={activeHighlight}
                  onHighlightChange={setActiveHighlight}
                />
              </div>
              <div className="col-span-9">
                <TripHighlightsContent
                  activeHighlight={activeHighlight}
                  packageData={contentData}
                  onContentChange={handleContentChange}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="itinerary" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <AddItineraryForm
                  editingItem={editingItem}
                  onAddItinerary={handleAddItinerary}
                  onUpdateItinerary={handleUpdateItinerary}
                  onCancelEdit={handleCancelEdit}
                />
              </div>
              <div>
                <ItineraryList
                  items={itineraryItems}
                  onEdit={handleEditItinerary}
                  onDelete={handleDeleteItinerary}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="equipment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Equipment Management</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <AddEquipmentForm
                      editingEquipment={editingEquipment}
                      onAddEquipment={handleAddEquipment}
                      onUpdateEquipment={handleUpdateEquipment}
                      onCancelEdit={handleCancelEditEquipment}
                    />
                  </div>
                  <div>
                    <EquipmentList
                      items={equipmentItems}
                      onEdit={handleEditEquipment}
                      onDelete={handleDeleteEquipment}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cost">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <AddCostDateForm
                  editingItem={editingCostDate}
                  onAdd={handleAddCostDate}
                  onUpdate={handleUpdateCostDate}
                  onCancelEdit={handleCancelEditCostDate}
                />
              </div>
              <div>
                <CostDateList
                  items={costDateItems}
                  onEdit={handleEditCostDate}
                  onDelete={handleDeleteCostDate}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="discount">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <AddGroupDiscountForm
                  editingItem={editingGroupDiscount}
                  onAdd={handleAddGroupDiscount}
                  onUpdate={handleUpdateGroupDiscount}
                  onCancelEdit={handleCancelEditGroupDiscount}
                />
              </div>
              <div>
                <GroupDiscountList
                  items={groupDiscountItems}
                  onEdit={handleEditGroupDiscount}
                  onDelete={handleDeleteGroupDiscount}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="faq">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <AddFAQForm
                  editingItem={editingFaq}
                  onAdd={handleAddFaq}
                  onUpdate={handleUpdateFaq}
                  onCancelEdit={handleCancelEditFaq}
                />
              </div>
              <div>
                <FAQList
                  items={faqItems}
                  onEdit={handleEditFaq}
                  onDelete={handleDeleteFaq}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="review">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <AddReviewForm
                  editingItem={editingReview}
                  onAdd={handleAddReview}
                  onUpdate={handleUpdateReview}
                  onCancelEdit={handleCancelEditReview}
                />
              </div>
              <div>
                <ReviewList
                  items={reviewItems}
                  onEdit={handleEditReview}
                  onDelete={handleDeleteReview}
                />
              </div>
            </div>
          </TabsContent>


        </Tabs>
      </div>
    </div>
  )
}